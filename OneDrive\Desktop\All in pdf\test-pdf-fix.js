const fs = require("fs");
const path = require("path");
const converter = require("./utils/pdfConverter");

async function testPDFGeneration() {
  console.log("🧪 Testing PDF generation fixes...");
  const testDir = path.join(__dirname, "test-output");

  // Create test directory
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  try {
    // Test 1: Text to PDF conversion
    console.log("\n📝 Test 1: Text to PDF conversion");
    const textContent =
      "Hello World!\n\nThis is a test text file for PDF conversion.\n\nGenerated at: " +
      new Date().toLocaleString();
    const textFilePath = path.join(testDir, "test.txt");
    const textPdfPath = path.join(testDir, "test-text.pdf");

    fs.writeFileSync(textFilePath, textContent);
    await converter.convertTextFile(textFilePath, textPdfPath);

    if (fs.existsSync(textPdfPath)) {
      const stats = fs.statSync(textPdfPath);
      console.log(
        `✅ Text PDF created successfully! Size: ${stats.size} bytes`
      );

      if (stats.size === 0) {
        console.log("❌ ERROR: PDF file is empty (0 bytes)");
      } else {
        console.log("✅ PDF file has content");
      }
    } else {
      console.log("❌ Text PDF creation failed");
    }

    // Test 2: HTML to PDF conversion
    console.log("\n🌐 Test 2: HTML to PDF conversion");
    const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Test HTML to PDF</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    h1 { color: #333; }
                    p { line-height: 1.6; }
                </style>
            </head>
            <body>
                <h1>Test HTML Document</h1>
                <p>This is a test HTML document for PDF conversion.</p>
                <p>Generated at: ${new Date().toLocaleString()}</p>
                <ul>
                    <li>Item 1</li>
                    <li>Item 2</li>
                    <li>Item 3</li>
                </ul>
            </body>
            </html>
        `;

    const htmlPdfPath = path.join(testDir, "test-html.pdf");
    await converter.convertHtmlToPdf(htmlContent, htmlPdfPath);

    if (fs.existsSync(htmlPdfPath)) {
      const stats = fs.statSync(htmlPdfPath);
      console.log(
        `✅ HTML PDF created successfully! Size: ${stats.size} bytes`
      );

      if (stats.size === 0) {
        console.log("❌ ERROR: PDF file is empty (0 bytes)");
      } else {
        console.log("✅ PDF file has content");
      }
    } else {
      console.log("❌ HTML PDF creation failed");
    }

    // Test 3: Word document fallback conversion
    console.log("\n📄 Test 3: Word document fallback (simulated)");
    const wordHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                    h1, h2, h3 { color: #333; }
                </style>
            </head>
            <body>
                <h1>Simulated Word Document</h1>
                <p>This simulates the HTML output from a Word document conversion.</p>
                <p>Generated at: ${new Date().toLocaleString()}</p>
            </body>
            </html>
        `;

    const wordPdfPath = path.join(testDir, "test-word.pdf");
    await converter.convertHtmlToPdf(wordHtml, wordPdfPath);

    if (fs.existsSync(wordPdfPath)) {
      const stats = fs.statSync(wordPdfPath);
      console.log(
        `✅ Word PDF created successfully! Size: ${stats.size} bytes`
      );

      if (stats.size === 0) {
        console.log("❌ ERROR: PDF file is empty (0 bytes)");
      } else {
        console.log("✅ PDF file has content");
      }
    } else {
      console.log("❌ Word PDF creation failed");
    }

    // Summary
    console.log("\n📊 Test Summary:");
    const files = fs.readdirSync(testDir).filter((f) => f.endsWith(".pdf"));
    console.log(`Generated ${files.length} PDF files:`);

    files.forEach((file) => {
      const filePath = path.join(testDir, file);
      const stats = fs.statSync(filePath);
      const status = stats.size > 0 ? "✅" : "❌";
      console.log(`  ${status} ${file}: ${stats.size} bytes`);
    });

    if (
      files.every((file) => {
        const filePath = path.join(testDir, file);
        const stats = fs.statSync(filePath);
        return stats.size > 0;
      })
    ) {
      console.log(
        "\n🎉 ALL TESTS PASSED! PDF generation is working correctly."
      );
    } else {
      console.log(
        "\n⚠️  Some tests failed. Check the output above for details."
      );
    }
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
    console.error("Stack trace:", error.stack);
  } finally {
    await converter.cleanup();
    console.log("\n🧹 Cleanup completed.");
  }
}

// Run the test
testPDFGeneration().catch(console.error);
