.converter-main {
  min-height: 100vh;
  padding-top: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.converter-header {
  color: white;
  margin-bottom: 3rem;
}

.converter-icon {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.converter-icon i {
  font-size: 3rem;
  color: white;
}

.converter-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.converter-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
}

.upload-container {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.upload-area {
  border: 3px dashed #d1d5db;
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  background: #f9fafb;
}

.upload-area:hover,
.upload-area.dragover {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.05);
  transform: scale(1.02);
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.upload-area h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.upload-area p {
  color: #6b7280;
  margin-bottom: 2rem;
}

#fileInput {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  pointer-events: all;
}

.upload-formats {
  margin-top: 1rem;
}

.upload-formats span {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: block;
}

.format-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.format-tag {
  background: var(--gradient);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.file-list {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.file-list h4 {
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 600;
}

.files-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: between;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.file-item:hover {
  background: #f3f4f6;
  border-color: var(--primary-color);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.file-icon i {
  color: white;
  font-size: 1.2rem;
}

.file-details h6 {
  margin: 0;
  font-weight: 600;
  color: var(--dark-color);
}

.file-details small {
  color: #6b7280;
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-remove {
  background: none;
  border: none;
  color: #ef4444;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.btn-remove:hover {
  background: rgba(239, 68, 68, 0.1);
}

.conversion-options {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.conversion-options h4 {
  color: var(--dark-color);
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.convert-section {
  margin-top: 2rem;
  text-align: center;
}

.btn-convert {
  background: var(--gradient);
  border: none;
  color: white;
  padding: 1rem 3rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.btn-convert:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
}

.btn-convert:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.progress-container {
  margin-top: 2rem;
  padding: 2rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--dark-color);
}

.progress {
  height: 12px;
  background: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--gradient);
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.results-container {
  margin-top: 2rem;
}

.result-success {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 16px;
}

.result-success i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.result-success h4 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.result-success p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.result-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-download {
  background: white;
  color: var(--success-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-download:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Dark Mode */
body.dark-mode .upload-container {
  background: #374151;
  color: #e5e7eb;
}

body.dark-mode .upload-area {
  background: #4b5563;
  border-color: #6b7280;
}

body.dark-mode .upload-area:hover,
body.dark-mode .upload-area.dragover {
  background: rgba(99, 102, 241, 0.1);
}

body.dark-mode .file-item {
  background: #4b5563;
  border-color: #6b7280;
  color: #e5e7eb;
}

body.dark-mode .progress-container {
  background: #4b5563;
  border-color: #6b7280;
}

/* Responsive */
@media (max-width: 768px) {
  .converter-main {
    padding-top: 100px;
  }
  
  .converter-title {
    font-size: 2rem;
  }
  
  .upload-area {
    padding: 2rem 1rem;
  }
  
  .upload-container {
    padding: 1.5rem;
  }
  
  .result-actions {
    flex-direction: column;
  }
}
