# ConvertifyPDF - Professional PDF Conversion Platform

A comprehensive web application for converting various file formats to PDF with download and preview functionality.

## Features

- **Multiple File Format Support**: Convert Word, Excel, PowerPoint, Images, Text, HTML, and CSV files to PDF
- **Real-time Conversion**: Backend API with Node.js and Express
- **File Preview**: View converted PDFs before downloading
- **Download Management**: Direct download of converted files
- **Responsive Design**: Works on desktop and mobile devices
- **Professional UI**: Clean, modern interface with Bootstrap

## Supported File Formats

### Office Documents
- **Word**: .doc, .docx
- **Excel**: .xls, .xlsx  
- **PowerPoint**: .ppt, .pptx

### Images
- **Formats**: .jpg, .jpeg, .png, .gif, .bmp, .webp
- **Multi-image**: Combine multiple images into single PDF

### Text & Web
- **Text**: .txt files with formatting
- **HTML**: .html, .htm files
- **CSV**: .csv files converted to formatted tables

## Installation & Setup

### Prerequisites
- Node.js (v16.0.0 or higher)
- npm (v8.0.0 or higher)

### Backend Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start the Server**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

3. **Server Configuration**
   - Server runs on `http://localhost:3000`
   - API endpoints available at `http://localhost:3000/api`
   - File uploads limited to 100MB
   - Automatic cleanup of old files (24 hours)

### Frontend Setup

1. **Open the Application**
   - Open `index.html` in a web browser
   - Or serve using a local web server for better CORS handling

2. **Configure API URL**
   - Backend API URL is set to `http://localhost:3000/api` in `convert.js`
   - Update `API_BASE_URL` if running on different port/domain

## API Endpoints

### File Conversion
```
POST /api/convert
- Upload file for conversion
- Returns: { conversionId, outputFileName, fileSize, status }
```

### Download Converted File
```
GET /api/download/:conversionId
- Download the converted PDF file
- Returns: PDF file stream
```

### Preview Converted File
```
GET /api/preview/:conversionId
- Preview PDF in browser
- Returns: PDF file for inline viewing
```

### Check Conversion Status
```
GET /api/status/:conversionId
- Check conversion progress/status
- Returns: { status, progress, error? }
```

## File Structure

```
ConvertifyPDF/
├── server.js                 # Main Express server
├── package.json             # Dependencies and scripts
├── utils/
│   ├── pdfConverter.js      # PDF conversion utilities
│   └── fileHandler.js       # File management utilities
├── uploads/                 # Temporary upload directory
├── converted/               # Converted files directory
├── index.html              # Main landing page
├── convert.html            # Conversion interface
├── convert.js              # Frontend conversion logic
├── about.html              # About page
├── contact.html            # Contact page
├── pricing.html            # Pricing page
└── styles.css              # Application styles
```

## Usage

### Web Interface

1. **Select Conversion Type**
   - Choose from Word, Excel, PowerPoint, Image, Text, HTML, or CSV conversion

2. **Upload Files**
   - Drag and drop files or click to browse
   - Multiple files supported for batch conversion

3. **Convert**
   - Click "Convert to PDF" button
   - Monitor real-time progress

4. **Download & Preview**
   - Preview converted PDFs in browser
   - Download individual files or batch download

### API Usage

```javascript
// Convert file
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('conversionType', 'word');

const response = await fetch('http://localhost:3000/api/convert', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log('Conversion ID:', result.conversionId);

// Download converted file
window.open(`http://localhost:3000/api/download/${result.conversionId}`);
```

## Dependencies

### Backend
- **express**: Web framework
- **multer**: File upload handling
- **puppeteer**: HTML to PDF conversion
- **office-to-pdf**: Office document conversion
- **sharp**: Image processing
- **pdfkit**: PDF generation
- **cors**: Cross-origin resource sharing
- **helmet**: Security middleware
- **compression**: Response compression

### Frontend
- **Bootstrap 5**: UI framework
- **Font Awesome**: Icons
- **Vanilla JavaScript**: No additional frameworks

## Configuration

### Environment Variables
```bash
PORT=3000                    # Server port
MAX_FILE_SIZE=104857600     # Max upload size (100MB)
CLEANUP_INTERVAL=3600000    # Cleanup interval (1 hour)
FILE_RETENTION=86400000     # File retention (24 hours)
```

### Security Features
- File type validation
- File size limits
- CORS protection
- Rate limiting
- Helmet security headers
- Input sanitization

## Development

### Scripts
```bash
npm start          # Start production server
npm run dev        # Start development server with nodemon
npm test           # Run tests
npm run lint       # Run ESLint
```

### Adding New File Types

1. **Update PDF Converter**
   - Add conversion logic in `utils/pdfConverter.js`
   - Update `supportedTypes` array

2. **Update Frontend**
   - Add file type to converter configuration
   - Update validation in `convert.js`

3. **Update File Handler**
   - Add MIME type mapping in `utils/fileHandler.js`

## Troubleshooting

### Common Issues

1. **"Cannot connect to server"**
   - Ensure backend server is running on port 3000
   - Check CORS configuration

2. **"File too large" error**
   - Check file size limits (default 100MB)
   - Increase limits in server configuration

3. **Conversion fails**
   - Check file format is supported
   - Verify file is not corrupted
   - Check server logs for detailed errors

### Logs
- Server logs available in console
- File operations logged with timestamps
- Error details included in API responses

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Check the troubleshooting section
- Review server logs
- Open an issue on the project repository
