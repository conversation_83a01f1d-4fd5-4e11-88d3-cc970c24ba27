document.addEventListener("DOMContentLoaded", function () {
  const dropArea = document.getElementById("drop-area");
  const fileInput = document.getElementById("fileInput");
  const form = document.getElementById("upload-form");
  const result = document.getElementById("result");
  const downloadLink = document.getElementById("downloadLink");

  ["dragenter", "dragover"].forEach(eventName => {
    dropArea.addEventListener(eventName, e => {
      e.preventDefault();
      dropArea.classList.add("highlight");
    });
  });

  ["dragleave", "drop"].forEach(eventName => {
    dropArea.addEventListener(eventName, e => {
      e.preventDefault();
      dropArea.classList.remove("highlight");
    });
  });

  dropArea.addEventListener("drop", e => {
    const files = e.dataTransfer.files;
    fileInput.files = files;
  });

  form.addEventListener("submit", function (e) {
    e.preventDefault();

    const file = fileInput.files[0];
    if (!file) {
      alert("Please select a file first.");
      return;
    }

    // Fake conversion action (simulate delay)
    setTimeout(() => {
      result.classList.remove("d-none");
      downloadLink.href = URL.createObjectURL(new Blob(["PDF Content Here"], { type: 'application/pdf' }));
      downloadLink.download = file.name.replace(/\.[^/.]+$/, ".pdf");
    }, 2000);
  });
});
// Theme Toggle
document.addEventListener('DOMContentLoaded', function() {
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = document.querySelector('.theme-icon');
  
  // Check for saved theme preference
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) {
    document.body.classList.toggle('dark-mode', savedTheme === 'dark');
    updateThemeIcon();
  }
  
  themeToggle?.addEventListener('click', function() {
    document.body.classList.toggle('dark-mode');
    const isDark = document.body.classList.contains('dark-mode');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
    updateThemeIcon();
  });
  
  function updateThemeIcon() {
    const isDark = document.body.classList.contains('dark-mode');
    if (themeIcon) {
      themeIcon.className = isDark ? 'fas fa-sun theme-icon' : 'fas fa-moon theme-icon';
    }
  }
});

// Converter Navigation
function openConverter(type) {
  const converterData = {
    word: {
      title: 'Word to PDF Converter',
      subtitle: 'Convert your Word documents to PDF format instantly',
      icon: 'fas fa-file-word',
      formats: ['.DOC', '.DOCX'],
      accept: '.doc,.docx'
    },
    excel: {
      title: 'Excel to PDF Converter',
      subtitle: 'Transform spreadsheets into professional PDFs',
      icon: 'fas fa-file-excel',
      formats: ['.XLS', '.XLSX'],
      accept: '.xls,.xlsx'
    },
    powerpoint: {
      title: 'PowerPoint to PDF Converter',
      subtitle: 'Convert presentations to shareable PDF format',
      icon: 'fas fa-file-powerpoint',
      formats: ['.PPT', '.PPTX'],
      accept: '.ppt,.pptx'
    },
    image: {
      title: 'Image to PDF Converter',
      subtitle: 'Combine images into a single PDF document',
      icon: 'fas fa-images',
      formats: ['.JPG', '.PNG', '.GIF', '.BMP'],
      accept: '.jpg,.jpeg,.png,.gif,.bmp'
    },
    text: {
      title: 'Text to PDF Converter',
      subtitle: 'Convert plain text files to formatted PDFs',
      icon: 'fas fa-file-alt',
      formats: ['.TXT'],
      accept: '.txt'
    },
    html: {
      title: 'HTML to PDF Converter',
      subtitle: 'Convert web pages and HTML files to PDF',
      icon: 'fas fa-code',
      formats: ['.HTML', '.HTM'],
      accept: '.html,.htm'
    },
    compress: {
      title: 'PDF Compressor',
      subtitle: 'Reduce PDF file size without quality loss',
      icon: 'fas fa-compress-alt',
      formats: ['.PDF'],
      accept: '.pdf'
    },
    merge: {
      title: 'PDF Merger',
      subtitle: 'Combine multiple PDF files into one document',
      icon: 'fas fa-object-group',
      formats: ['.PDF'],
      accept: '.pdf'
    },
    split: {
      title: 'PDF Splitter',
      subtitle: 'Extract pages or split PDF into multiple files',
      icon: 'fas fa-cut',
      formats: ['.PDF'],
      accept: '.pdf'
    },
    ocr: {
      title: 'OCR PDF Converter',
      subtitle: 'Extract text from scanned documents',
      icon: 'fas fa-eye',
      formats: ['.PDF', '.JPG', '.PNG'],
      accept: '.pdf,.jpg,.jpeg,.png'
    },
    protect: {
      title: 'PDF Protector',
      subtitle: 'Add password and security to your PDFs',
      icon: 'fas fa-shield-alt',
      formats: ['.PDF'],
      accept: '.pdf'
    },
    unlock: {
      title: 'PDF Unlocker',
      subtitle: 'Remove password protection from PDFs',
      icon: 'fas fa-unlock',
      formats: ['.PDF'],
      accept: '.pdf'
    }
  };
  
  // Store converter data in sessionStorage
  sessionStorage.setItem('converterType', type);
  sessionStorage.setItem('converterData', JSON.stringify(converterData[type]));
  
  // Navigate to converter page
  window.location.href = 'convert.html';
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Navbar scroll effect
window.addEventListener('scroll', function() {
  const navbar = document.querySelector('.custom-navbar');
  if (navbar) {
    if (window.scrollY > 50) {
      navbar.style.background = 'rgba(255, 255, 255, 0.98)';
      navbar.style.backdropFilter = 'blur(20px)';
    } else {
      navbar.style.background = 'rgba(255, 255, 255, 0.95)';
    }
  }
});

// Intersection Observer for animations
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = '1';
      entry.target.style.transform = 'translateY(0)';
    }
  });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
  const animateElements = document.querySelectorAll('.tool-card, .feature-card');
  animateElements.forEach(el => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(el);
  });
});
