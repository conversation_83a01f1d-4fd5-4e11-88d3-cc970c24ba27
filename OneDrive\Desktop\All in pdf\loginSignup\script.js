(function () {
  'use strict';

  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', function (event) {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      } else {
        event.preventDefault();
        alert(`${form.id === "loginForm" ? "Login" : "Signup"} Successful!`);
        form.reset();
      }
      form.classList.add('was-validated');
    }, false);
  });
})();
