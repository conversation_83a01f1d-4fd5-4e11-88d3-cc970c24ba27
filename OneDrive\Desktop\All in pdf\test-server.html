<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ConvertifyPDF Backend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            min-height: 20px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .download-link {
            display: inline-block;
            margin: 10px 0;
            padding: 8px 16px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .download-link:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ConvertifyPDF Backend Test</h1>
        
        <div class="test-section">
            <h3>📄 Server Status Test</h3>
            <button onclick="testServerStatus()">Test Server Connection</button>
            <div id="serverStatus" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 Text to PDF Conversion Test</h3>
            <p>Test converting a simple text file to PDF:</p>
            <button onclick="testTextConversion()">Test Text Conversion</button>
            <div id="textResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🖼️ Image to PDF Conversion Test</h3>
            <p>Select an image file to convert to PDF:</p>
            <input type="file" id="imageFile" accept=".jpg,.jpeg,.png,.gif,.bmp,.webp">
            <button onclick="testImageConversion()">Convert Image to PDF</button>
            <div id="imageResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Excel to PDF Conversion Test</h3>
            <p>Select an Excel file (.xlsx) to convert to PDF:</p>
            <input type="file" id="excelFile" accept=".xlsx">
            <button onclick="testExcelConversion()">Convert Excel to PDF</button>
            <div id="excelResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📄 Word to PDF Conversion Test</h3>
            <p>Select a Word file (.docx) to convert to PDF:</p>
            <input type="file" id="wordFile" accept=".docx">
            <button onclick="testWordConversion()">Convert Word to PDF</button>
            <div id="wordResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000/api';

        async function testServerStatus() {
            const resultDiv = document.getElementById('serverStatus');
            resultDiv.innerHTML = '<div class="info">Testing server connection...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/status/test`);
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ Server is running and accessible!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Server responded with error: ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Cannot connect to server. Make sure the backend is running on port 3000.<br>Error: ' + error.message + '</div>';
            }
        }

        async function testTextConversion() {
            const resultDiv = document.getElementById('textResult');
            resultDiv.innerHTML = '<div class="info">Creating and converting text file...</div>';
            
            try {
                // Create a simple text file
                const textContent = 'Hello World!\n\nThis is a test text file for PDF conversion.\n\nGenerated at: ' + new Date().toLocaleString();
                const blob = new Blob([textContent], { type: 'text/plain' });
                const file = new File([blob], 'test.txt', { type: 'text/plain' });
                
                const result = await convertFile(file, 'text');
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Text conversion successful!</div>
                        <div><strong>File:</strong> ${result.outputFileName}</div>
                        <div><strong>Size:</strong> ${formatFileSize(result.fileSize)}</div>
                        <a href="${API_BASE_URL}/download/${result.conversionId}" class="download-link" target="_blank">📥 Download PDF</a>
                        <a href="${API_BASE_URL}/preview/${result.conversionId}" class="download-link" target="_blank">👁️ Preview PDF</a>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Conversion failed: ' + result.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }

        async function testImageConversion() {
            const fileInput = document.getElementById('imageFile');
            const resultDiv = document.getElementById('imageResult');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">❌ Please select an image file first</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Converting image to PDF...</div>';
            
            try {
                const result = await convertFile(fileInput.files[0], 'image');
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Image conversion successful!</div>
                        <div><strong>File:</strong> ${result.outputFileName}</div>
                        <div><strong>Size:</strong> ${formatFileSize(result.fileSize)}</div>
                        <a href="${API_BASE_URL}/download/${result.conversionId}" class="download-link" target="_blank">📥 Download PDF</a>
                        <a href="${API_BASE_URL}/preview/${result.conversionId}" class="download-link" target="_blank">👁️ Preview PDF</a>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Conversion failed: ' + result.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }

        async function testExcelConversion() {
            const fileInput = document.getElementById('excelFile');
            const resultDiv = document.getElementById('excelResult');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">❌ Please select an Excel file (.xlsx) first</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Converting Excel to PDF...</div>';
            
            try {
                const result = await convertFile(fileInput.files[0], 'excel');
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Excel conversion successful!</div>
                        <div><strong>File:</strong> ${result.outputFileName}</div>
                        <div><strong>Size:</strong> ${formatFileSize(result.fileSize)}</div>
                        <a href="${API_BASE_URL}/download/${result.conversionId}" class="download-link" target="_blank">📥 Download PDF</a>
                        <a href="${API_BASE_URL}/preview/${result.conversionId}" class="download-link" target="_blank">👁️ Preview PDF</a>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Conversion failed: ' + result.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }

        async function testWordConversion() {
            const fileInput = document.getElementById('wordFile');
            const resultDiv = document.getElementById('wordResult');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">❌ Please select a Word file (.docx) first</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Converting Word to PDF...</div>';
            
            try {
                const result = await convertFile(fileInput.files[0], 'word');
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Word conversion successful!</div>
                        <div><strong>File:</strong> ${result.outputFileName}</div>
                        <div><strong>Size:</strong> ${formatFileSize(result.fileSize)}</div>
                        <a href="${API_BASE_URL}/download/${result.conversionId}" class="download-link" target="_blank">📥 Download PDF</a>
                        <a href="${API_BASE_URL}/preview/${result.conversionId}" class="download-link" target="_blank">👁️ Preview PDF</a>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Conversion failed: ' + result.error + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }

        async function convertFile(file, conversionType) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('conversionType', conversionType);

            const response = await fetch(`${API_BASE_URL}/convert`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Conversion failed');
            }

            const result = await response.json();
            return { success: true, ...result };
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Test server connection on page load
        window.addEventListener('load', testServerStatus);
    </script>
</body>
</html>
