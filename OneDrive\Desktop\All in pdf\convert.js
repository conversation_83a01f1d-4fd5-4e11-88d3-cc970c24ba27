// Backend integration constants
const API_BASE_URL = "http://localhost:3000/api";
let selectedFiles = [];
let currentConversionType = "word";

document.addEventListener("DOMContentLoaded", function () {
  // Get converter data from sessionStorage
  const converterType = sessionStorage.getItem("converterType") || "word";
  const converterData = JSON.parse(sessionStorage.getItem("converterData")) || {
    title: "Word to PDF Converter",
    subtitle: "Convert your Word documents to PDF format instantly",
    icon: "fas fa-file-word",
    formats: [".DOC", ".DOCX"],
    accept: ".doc,.docx",
  };

  currentConversionType = converterType;

  // Update page content
  updateConverterUI(converterData);

  // Initialize drag and drop
  initializeDragDrop();

  // Initialize file handling
  initializeFileHandling();

  // Initialize conversion
  initializeConversion();
});

function updateConverterUI(data) {
  document.getElementById("converterTitle").textContent = data.title;
  document.getElementById("converterSubtitle").textContent = data.subtitle;
  document.getElementById(
    "converterIcon"
  ).innerHTML = `<i class="${data.icon}"></i>`;
  document.getElementById("fileInput").setAttribute("accept", data.accept);

  const formatTags = document.getElementById("formatTags");
  formatTags.innerHTML = data.formats
    .map((format) => `<span class="format-tag">${format}</span>`)
    .join("");
}

function initializeDragDrop() {
  const uploadArea = document.getElementById("uploadArea");
  const fileInput = document.getElementById("fileInput");

  // Prevent default drag behaviors
  ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
    uploadArea.addEventListener(eventName, preventDefaults, false);
    document.body.addEventListener(eventName, preventDefaults, false);
  });

  // Highlight drop area when item is dragged over it
  ["dragenter", "dragover"].forEach((eventName) => {
    uploadArea.addEventListener(eventName, highlight, false);
  });

  ["dragleave", "drop"].forEach((eventName) => {
    uploadArea.addEventListener(eventName, unhighlight, false);
  });

  // Handle dropped files
  uploadArea.addEventListener("drop", handleDrop, false);

  // Handle file input change
  fileInput.addEventListener("change", function (e) {
    handleFiles(e.target.files);
  });

  // Click to upload
  uploadArea.addEventListener("click", () => fileInput.click());
}

function preventDefaults(e) {
  e.preventDefault();
  e.stopPropagation();
}

function highlight(e) {
  document.getElementById("uploadArea").classList.add("dragover");
}

function unhighlight(e) {
  document.getElementById("uploadArea").classList.remove("dragover");
}

function handleDrop(e) {
  const dt = e.dataTransfer;
  const files = dt.files;
  handleFiles(files);
}

function handleFiles(files) {
  selectedFiles = Array.from(files);
  displayFiles();
  showConversionOptions();
}

function displayFiles() {
  const fileList = document.getElementById("fileList");
  const filesContainer = document.getElementById("filesContainer");

  if (selectedFiles.length === 0) {
    fileList.style.display = "none";
    return;
  }

  fileList.style.display = "block";
  filesContainer.innerHTML = "";

  selectedFiles.forEach((file, index) => {
    const fileItem = createFileItem(file, index);
    filesContainer.appendChild(fileItem);
  });
}

function createFileItem(file, index) {
  const fileItem = document.createElement("div");
  fileItem.className = "file-item";

  const fileIcon = getFileIcon(file.name);
  const fileSize = formatFileSize(file.size);

  fileItem.innerHTML = `
    <div class="file-info">
      <div class="file-icon">
        <i class="${fileIcon}"></i>
      </div>
      <div class="file-details">
        <h6>${file.name}</h6>
        <small>${fileSize}</small>
      </div>
    </div>
    <div class="file-actions">
      <button class="btn-remove" onclick="removeFile(${index})">
        <i class="fas fa-times"></i>
      </button>
    </div>
  `;

  return fileItem;
}

function getFileIcon(filename) {
  const extension = filename.split(".").pop().toLowerCase();
  const iconMap = {
    doc: "fas fa-file-word",
    docx: "fas fa-file-word",
    xls: "fas fa-file-excel",
    xlsx: "fas fa-file-excel",
    ppt: "fas fa-file-powerpoint",
    pptx: "fas fa-file-powerpoint",
    pdf: "fas fa-file-pdf",
    txt: "fas fa-file-alt",
    jpg: "fas fa-file-image",
    jpeg: "fas fa-file-image",
    png: "fas fa-file-image",
    gif: "fas fa-file-image",
    html: "fas fa-file-code",
    htm: "fas fa-file-code",
  };

  return iconMap[extension] || "fas fa-file";
}

function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function removeFile(index) {
  selectedFiles.splice(index, 1);
  displayFiles();

  if (selectedFiles.length === 0) {
    hideConversionOptions();
  }
}

function showConversionOptions() {
  document.getElementById("conversionOptions").style.display = "block";
  document.getElementById("convertSection").style.display = "block";
}

function hideConversionOptions() {
  document.getElementById("conversionOptions").style.display = "none";
  document.getElementById("convertSection").style.display = "none";
}

function initializeFileHandling() {
  // File input handling is already initialized in initializeDragDrop
}

function initializeConversion() {
  const convertBtn = document.getElementById("convertBtn");
  const convertAnotherBtn = document.getElementById("convertAnotherBtn");

  convertBtn.addEventListener("click", startConversion);
  convertAnotherBtn.addEventListener("click", resetConverter);
}

function startConversion() {
  if (selectedFiles.length === 0) {
    alert("Please select files to convert");
    return;
  }

  const convertBtn = document.getElementById("convertBtn");
  const progressContainer = document.getElementById("progressContainer");
  const progressBar = document.getElementById("progressBar");
  const progressPercent = document.getElementById("progressPercent");

  // Disable convert button
  convertBtn.disabled = true;
  convertBtn.innerHTML =
    '<i class="fas fa-spinner fa-spin me-2"></i>Converting...';

  // Show progress
  progressContainer.style.display = "block";

  // Simulate conversion progress
  let progress = 0;
  const interval = setInterval(() => {
    progress += Math.random() * 15;
    if (progress > 100) progress = 100;

    progressBar.style.width = progress + "%";
    progressPercent.textContent = Math.round(progress) + "%";

    if (progress >= 100) {
      clearInterval(interval);
      setTimeout(() => {
        showResults();
      }, 500);
    }
  }, 200);

  // In a real application, you would send files to the server here
  // sendFilesToServer(selectedFiles);
}

function showResults() {
  document.getElementById("progressContainer").style.display = "none";
  document.getElementById("resultsContainer").style.display = "block";

  // Setup download functionality
  const downloadBtn = document.getElementById("downloadBtn");
  downloadBtn.addEventListener("click", downloadConvertedFile);
}

function downloadConvertedFile() {
  // In a real application, this would download the actual converted file
  // For demo purposes, we'll create a dummy PDF blob
  const dummyPDF = new Blob(
    [
      "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF",
    ],
    { type: "application/pdf" }
  );

  const url = URL.createObjectURL(dummyPDF);
  const a = document.createElement("a");
  a.href = url;
  a.download = "converted-file.pdf";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

function resetConverter() {
  selectedFiles = [];
  document.getElementById("fileList").style.display = "none";
  document.getElementById("conversionOptions").style.display = "none";
  document.getElementById("convertSection").style.display = "none";
  document.getElementById("resultsContainer").style.display = "none";
  document.getElementById("fileInput").value = "";
  document.getElementById("filesContainer").innerHTML = "";
}

// Backend integration functions
async function convertFileToAPI(file) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("conversionType", currentConversionType);

  const response = await fetch(`${API_BASE_URL}/convert`, {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Conversion failed");
  }

  return await response.json();
}

function previewFile(conversionId) {
  const previewUrl = `${API_BASE_URL}/preview/${conversionId}`;
  window.open(previewUrl, "_blank");
}

function downloadFile(conversionId, filename) {
  const downloadUrl = `${API_BASE_URL}/download/${conversionId}`;

  // Create temporary link and trigger download
  const link = document.createElement("a");
  link.href = downloadUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  showNotification(`Downloading ${filename}...`, "info");
}

function showNotification(message, type = "info") {
  // Create notification element
  const notification = document.createElement("div");
  notification.className = `alert alert-${
    type === "error" ? "danger" : type
  } alert-dismissible fade show position-fixed`;
  notification.style.cssText =
    "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";

  notification.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  // Add to page
  document.body.appendChild(notification);

  // Auto remove after 5 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, 5000);
}
