:root {
  --primary-color: #6366f1;
  --secondary-color: #8b5cf6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  --dark-color: #1f2937;
  --light-color: #f8fafc;
  --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
  --border-radius: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: var(--dark-color);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  transition: var(--transition);
}

/* Enhanced Navbar */
.custom-navbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 0;
  transition: var(--transition);
}

.custom-navbar .navbar-brand {
  color: var(--primary-color) !important;
  font-weight: 700;
  font-size: 1.8rem;
}

.brand-icon {
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.custom-navbar .nav-link {
  color: var(--dark-color) !important;
  font-weight: 500;
  margin: 0 0.5rem;
  padding: 0.5rem 1rem !important;
  border-radius: 8px;
  transition: var(--transition);
}

.custom-navbar .nav-link:hover,
.custom-navbar .nav-link.active {
  background: var(--gradient);
  color: white !important;
  transform: translateY(-2px);
}

.theme-btn {
  border-radius: 50px !important;
  padding: 0.5rem 1rem !important;
  transition: var(--transition);
}

.theme-btn:hover {
  transform: scale(1.05);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.text-gradient {
  background: linear-gradient(45deg, #ffd700, #ff6b6b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-item h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #ffd700;
}

.stat-item p {
  opacity: 0.8;
  font-size: 0.9rem;
}

.floating-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius);
  padding: 3rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Tools Section */
.tools-section {
  background: white;
  position: relative;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

/* Enhanced Tool Cards */
.tool-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow);
  border: 1px solid #e5e7eb;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 100%;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: var(--transition);
}

.tool-card:hover::before {
  left: 100%;
}

.tool-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
  border-color: var(--primary-color);
}

.tool-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: var(--transition);
}

.tool-icon i {
  font-size: 2rem;
  color: white;
}

.tool-icon.excel {
  background: linear-gradient(135deg, #10b981, #059669);
}

.tool-icon.powerpoint {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.tool-icon.image {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.tool-icon.text {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.tool-icon.html {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.tool-icon.compress {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.tool-icon.merge {
  background: linear-gradient(135deg, #14b8a6, #0d9488);
}

.tool-icon.split {
  background: linear-gradient(135deg, #f97316, #ea580c);
}

.tool-icon.ocr {
  background: linear-gradient(135deg, #84cc16, #65a30d);
}

.tool-icon.protect {
  background: linear-gradient(135deg, #ec4899, #db2777);
}

.tool-icon.unlock {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.tool-card:hover .tool-icon {
  transform: scale(1.1) rotate(5deg);
}

.tool-card h5 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--dark-color);
}

.tool-card p {
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
}

.tool-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--gradient);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.tool-badge.premium {
  background: linear-gradient(135deg, #ffd700, #ff6b6b);
}

/* Features Section */
.features-section {
  background: #f8fafc !important;
}

.feature-card {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.feature-icon {
  font-size: 3rem;
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.feature-card h5 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.feature-card p {
  color: #6b7280;
  font-size: 0.95rem;
}

/* Footer */
.footer {
  background: var(--dark-color);
  color: white;
}

.footer-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-subtitle {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #d1d5db;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #9ca3af;
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: white;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: var(--gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: var(--transition);
}

.social-links a:hover {
  transform: translateY(-3px);
}

.footer-divider {
  border-color: #374151;
  margin: 2rem 0 1rem;
}

/* Dark Mode */
body.dark-mode {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: #e5e7eb;
}

body.dark-mode .custom-navbar {
  background: rgba(31, 41, 55, 0.95) !important;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .custom-navbar .nav-link {
  color: #e5e7eb !important;
}

body.dark-mode .tool-card {
  background: #374151;
  border-color: #4b5563;
  color: #e5e7eb;
}

body.dark-mode .feature-card {
  background: #374151;
  color: #e5e7eb;
}

body.dark-mode .tools-section {
  background: #1f2937;
}

body.dark-mode .section-title {
  color: #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }
  
  .tool-card {
    padding: 1.5rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .custom-navbar .navbar-brand {
    font-size: 1.5rem;
  }
}
