const fs = require('fs');
const path = require('path');

class FileHandler {
  /**
   * Clean up old files in a directory
   * @param {string} directory - Directory path to clean
   * @param {number} maxAge - Maximum age in milliseconds
   */
  static cleanupOldFiles(directory, maxAge = 24 * 60 * 60 * 1000) {
    try {
      if (!fs.existsSync(directory)) {
        return;
      }

      const files = fs.readdirSync(directory);
      const now = Date.now();
      let deletedCount = 0;

      files.forEach(file => {
        const filePath = path.join(directory, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          try {
            fs.unlinkSync(filePath);
            deletedCount++;
            console.log(`Deleted old file: ${file}`);
          } catch (error) {
            console.error(`Failed to delete file ${file}:`, error.message);
          }
        }
      });

      if (deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} old files from ${directory}`);
      }
    } catch (error) {
      console.error(`Error cleaning up directory ${directory}:`, error.message);
    }
  }

  /**
   * Get file size in human readable format
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Validate file type
   * @param {string} filename - Original filename
   * @param {string} mimetype - File MIME type
   * @returns {object} Validation result
   */
  static validateFile(filename, mimetype) {
    const allowedTypes = {
      // Office documents
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      
      // Text files
      'text/plain': ['.txt'],
      'text/html': ['.html', '.htm'],
      'text/csv': ['.csv'],
      
      // Images
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'image/bmp': ['.bmp'],
      'image/webp': ['.webp']
    };

    const extension = path.extname(filename).toLowerCase();
    
    // Check if MIME type is allowed
    if (!allowedTypes[mimetype]) {
      return {
        valid: false,
        error: `Unsupported file type: ${mimetype}`
      };
    }

    // Check if extension matches MIME type
    if (!allowedTypes[mimetype].includes(extension)) {
      return {
        valid: false,
        error: `File extension ${extension} doesn't match MIME type ${mimetype}`
      };
    }

    return { valid: true };
  }

  /**
   * Generate safe filename
   * @param {string} originalName - Original filename
   * @returns {string} Safe filename
   */
  static generateSafeFilename(originalName) {
    const extension = path.extname(originalName);
    const basename = path.basename(originalName, extension);
    
    // Remove unsafe characters
    const safeName = basename
      .replace(/[^a-zA-Z0-9\-_\s]/g, '')
      .replace(/\s+/g, '_')
      .substring(0, 100); // Limit length
    
    return safeName + extension;
  }

  /**
   * Check if file exists and is readable
   * @param {string} filePath - Path to file
   * @returns {boolean} True if file exists and is readable
   */
  static isFileAccessible(filePath) {
    try {
      fs.accessSync(filePath, fs.constants.F_OK | fs.constants.R_OK);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file information
   * @param {string} filePath - Path to file
   * @returns {object} File information
   */
  static getFileInfo(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const extension = path.extname(filePath).toLowerCase();
      
      return {
        size: stats.size,
        formattedSize: this.formatFileSize(stats.size),
        created: stats.birthtime,
        modified: stats.mtime,
        extension: extension,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory()
      };
    } catch (error) {
      throw new Error(`Failed to get file info: ${error.message}`);
    }
  }

  /**
   * Create directory if it doesn't exist
   * @param {string} dirPath - Directory path
   */
  static ensureDirectory(dirPath) {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
      }
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Copy file from source to destination
   * @param {string} source - Source file path
   * @param {string} destination - Destination file path
   */
  static copyFile(source, destination) {
    try {
      // Ensure destination directory exists
      const destDir = path.dirname(destination);
      this.ensureDirectory(destDir);
      
      fs.copyFileSync(source, destination);
      console.log(`File copied from ${source} to ${destination}`);
    } catch (error) {
      throw new Error(`Failed to copy file: ${error.message}`);
    }
  }

  /**
   * Move file from source to destination
   * @param {string} source - Source file path
   * @param {string} destination - Destination file path
   */
  static moveFile(source, destination) {
    try {
      // Ensure destination directory exists
      const destDir = path.dirname(destination);
      this.ensureDirectory(destDir);
      
      fs.renameSync(source, destination);
      console.log(`File moved from ${source} to ${destination}`);
    } catch (error) {
      throw new Error(`Failed to move file: ${error.message}`);
    }
  }

  /**
   * Delete file safely
   * @param {string} filePath - Path to file to delete
   */
  static deleteFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`Deleted file: ${filePath}`);
      }
    } catch (error) {
      console.error(`Failed to delete file ${filePath}:`, error.message);
    }
  }

  /**
   * Get directory size
   * @param {string} dirPath - Directory path
   * @returns {number} Total size in bytes
   */
  static getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const files = fs.readdirSync(dirPath);
      
      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile()) {
          totalSize += stats.size;
        } else if (stats.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        }
      });
    } catch (error) {
      console.error(`Error calculating directory size for ${dirPath}:`, error.message);
    }
    
    return totalSize;
  }

  /**
   * List files in directory with details
   * @param {string} dirPath - Directory path
   * @returns {Array} Array of file objects
   */
  static listFiles(dirPath) {
    try {
      const files = fs.readdirSync(dirPath);
      
      return files.map(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        return {
          name: file,
          path: filePath,
          size: stats.size,
          formattedSize: this.formatFileSize(stats.size),
          created: stats.birthtime,
          modified: stats.mtime,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          extension: path.extname(file).toLowerCase()
        };
      });
    } catch (error) {
      throw new Error(`Failed to list files in ${dirPath}: ${error.message}`);
    }
  }
}

module.exports = FileHandler;
