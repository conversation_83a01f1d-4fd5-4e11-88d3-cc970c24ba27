{"name": "convertify-pdf-backend", "version": "1.0.0", "description": "Backend server for ConvertifyPDF - Professional PDF conversion platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "install-deps": "npm install"}, "keywords": ["pdf", "conversion", "office", "documents", "images", "nodejs", "express"], "author": "ConvertifyPDF Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "uuid": "^9.0.1", "puppeteer": "^21.5.2", "sharp": "^0.32.6", "pdfkit": "^0.13.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "mammoth": "^1.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.54.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/convertifypdf/backend.git"}, "bugs": {"url": "https://github.com/convertifypdf/backend/issues"}, "homepage": "https://convertifypdf.com"}