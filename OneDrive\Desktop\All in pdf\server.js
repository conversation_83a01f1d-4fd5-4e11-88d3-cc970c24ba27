const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

// Import conversion utilities
const pdfConverter = require('./utils/pdfConverter');
const fileHandler = require('./utils/fileHandler');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

// Serve static files from current directory
app.use(express.static(__dirname));

// Create necessary directories
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');
const tempDir = path.join(__dirname, 'temp');

[uploadsDir, outputDir, tempDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow various file types for conversion
    const allowedTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/html',
      'text/csv',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'), false);
    }
  }
});

// Routes

// Home route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// File upload and conversion endpoint
app.post('/api/convert', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { conversionType = 'auto' } = req.body;
    const inputFile = req.file;
    const outputFileName = `${path.parse(inputFile.filename).name}.pdf`;
    const outputPath = path.join(outputDir, outputFileName);

    console.log(`Converting ${inputFile.originalname} to PDF...`);

    // Convert file to PDF based on type
    const result = await pdfConverter.convertToPDF({
      inputPath: inputFile.path,
      outputPath: outputPath,
      fileType: conversionType,
      originalName: inputFile.originalname,
      mimeType: inputFile.mimetype
    });

    if (!result.success) {
      throw new Error(result.error || 'Conversion failed');
    }

    // Generate unique ID for this conversion
    const conversionId = uuidv4();
    
    // Store conversion info in memory (in production, use a database)
    global.conversions = global.conversions || {};
    global.conversions[conversionId] = {
      originalName: inputFile.originalname,
      outputPath: outputPath,
      outputFileName: outputFileName,
      createdAt: new Date(),
      fileSize: fs.statSync(outputPath).size
    };

    // Clean up input file
    fs.unlinkSync(inputFile.path);

    res.json({
      success: true,
      conversionId: conversionId,
      originalName: inputFile.originalname,
      outputFileName: outputFileName,
      fileSize: fs.statSync(outputPath).size,
      downloadUrl: `/api/download/${conversionId}`,
      previewUrl: `/api/preview/${conversionId}`
    });

  } catch (error) {
    console.error('Conversion error:', error);
    
    // Clean up files on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({ 
      error: error.message || 'Conversion failed',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Download converted PDF
app.get('/api/download/:conversionId', (req, res) => {
  try {
    const { conversionId } = req.params;
    const conversion = global.conversions?.[conversionId];

    if (!conversion) {
      return res.status(404).json({ error: 'Conversion not found' });
    }

    if (!fs.existsSync(conversion.outputPath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Set headers for download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${conversion.outputFileName}"`);
    res.setHeader('Content-Length', conversion.fileSize);

    // Stream the file
    const fileStream = fs.createReadStream(conversion.outputPath);
    fileStream.pipe(res);

    fileStream.on('end', () => {
      console.log(`File downloaded: ${conversion.outputFileName}`);
    });

    fileStream.on('error', (error) => {
      console.error('Download error:', error);
      res.status(500).json({ error: 'Download failed' });
    });

  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ error: 'Download failed' });
  }
});

// Preview PDF in browser
app.get('/api/preview/:conversionId', (req, res) => {
  try {
    const { conversionId } = req.params;
    const conversion = global.conversions?.[conversionId];

    if (!conversion) {
      return res.status(404).json({ error: 'Conversion not found' });
    }

    if (!fs.existsSync(conversion.outputPath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Set headers for inline viewing
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="${conversion.outputFileName}"`);
    res.setHeader('Content-Length', conversion.fileSize);

    // Stream the file
    const fileStream = fs.createReadStream(conversion.outputPath);
    fileStream.pipe(res);

    fileStream.on('error', (error) => {
      console.error('Preview error:', error);
      res.status(500).json({ error: 'Preview failed' });
    });

  } catch (error) {
    console.error('Preview error:', error);
    res.status(500).json({ error: 'Preview failed' });
  }
});

// Get conversion status
app.get('/api/status/:conversionId', (req, res) => {
  try {
    const { conversionId } = req.params;
    const conversion = global.conversions?.[conversionId];

    if (!conversion) {
      return res.status(404).json({ error: 'Conversion not found' });
    }

    res.json({
      success: true,
      conversion: {
        id: conversionId,
        originalName: conversion.originalName,
        outputFileName: conversion.outputFileName,
        fileSize: conversion.fileSize,
        createdAt: conversion.createdAt,
        downloadUrl: `/api/download/${conversionId}`,
        previewUrl: `/api/preview/${conversionId}`
      }
    });

  } catch (error) {
    console.error('Status error:', error);
    res.status(500).json({ error: 'Failed to get status' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 100MB.' });
    }
    return res.status(400).json({ error: error.message });
  }
  
  res.status(500).json({ 
    error: 'Internal server error',
    details: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Cleanup old files periodically (every hour)
setInterval(() => {
  fileHandler.cleanupOldFiles(outputDir, 24 * 60 * 60 * 1000); // 24 hours
  fileHandler.cleanupOldFiles(uploadsDir, 60 * 60 * 1000); // 1 hour
}, 60 * 60 * 1000);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ConvertifyPDF Server running on port ${PORT}`);
  console.log(`📁 Uploads directory: ${uploadsDir}`);
  console.log(`📄 Output directory: ${outputDir}`);
  console.log(`🌐 Access at: http://localhost:${PORT}`);
});

module.exports = app;
