/* Reset and common styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #667eea, #764ba2);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  background-color: white;
  animation: slideIn 0.4s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.btn-primary, .btn-success {
  border-radius: 25px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.form-label {
  font-weight: 600;
}

a.link {
  color: #6c63ff;
  text-decoration: none;
  font-weight: 500;
  transition: 0.3s;
}

a.link:hover {
  text-decoration: underline;
  color: #5146d9;
}
