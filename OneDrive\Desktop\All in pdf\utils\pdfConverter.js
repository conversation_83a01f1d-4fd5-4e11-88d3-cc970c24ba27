const fs = require('fs');
const path = require('path');
const puppeteer = require('puppeteer');
const officeToPdf = require('office-to-pdf');
const sharp = require('sharp');
const PDFDocument = require('pdfkit');

class PDFConverter {
  constructor() {
    this.browser = null;
  }

  async initBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
    }
    return this.browser;
  }

  async convertToPDF(options) {
    const { inputPath, outputPath, fileType, originalName, mimeType } = options;

    try {
      // Determine conversion method based on file type
      const extension = path.extname(originalName).toLowerCase();
      
      switch (true) {
        case this.isOfficeDocument(mimeType, extension):
          return await this.convertOfficeDocument(inputPath, outputPath);
        
        case this.isImage(mimeType, extension):
          return await this.convertImage(inputPath, outputPath);
        
        case this.isTextFile(mimeType, extension):
          return await this.convertTextFile(inputPath, outputPath);
        
        case this.isHtmlFile(mimeType, extension):
          return await this.convertHtmlFile(inputPath, outputPath);
        
        case this.isCsvFile(mimeType, extension):
          return await this.convertCsvFile(inputPath, outputPath);
        
        default:
          throw new Error(`Unsupported file type: ${extension}`);
      }
    } catch (error) {
      console.error('PDF conversion error:', error);
      return { success: false, error: error.message };
    }
  }

  isOfficeDocument(mimeType, extension) {
    const officeTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ];
    const officeExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
    return officeTypes.includes(mimeType) || officeExtensions.includes(extension);
  }

  isImage(mimeType, extension) {
    const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageTypes.includes(mimeType) || imageExtensions.includes(extension);
  }

  isTextFile(mimeType, extension) {
    return mimeType === 'text/plain' || extension === '.txt';
  }

  isHtmlFile(mimeType, extension) {
    return mimeType === 'text/html' || extension === '.html' || extension === '.htm';
  }

  isCsvFile(mimeType, extension) {
    return mimeType === 'text/csv' || extension === '.csv';
  }

  async convertOfficeDocument(inputPath, outputPath) {
    try {
      const buffer = fs.readFileSync(inputPath);
      const pdfBuffer = await officeToPdf(buffer);
      fs.writeFileSync(outputPath, pdfBuffer);
      return { success: true };
    } catch (error) {
      throw new Error(`Office document conversion failed: ${error.message}`);
    }
  }

  async convertImage(inputPath, outputPath) {
    try {
      const doc = new PDFDocument({ autoFirstPage: false });
      const stream = fs.createWriteStream(outputPath);
      doc.pipe(stream);

      // Get image metadata
      const metadata = await sharp(inputPath).metadata();
      const { width, height } = metadata;

      // Calculate page size (A4 max, maintain aspect ratio)
      const maxWidth = 595; // A4 width in points
      const maxHeight = 842; // A4 height in points
      
      let pageWidth = width * 0.75; // Convert pixels to points (72 DPI)
      let pageHeight = height * 0.75;

      if (pageWidth > maxWidth) {
        const ratio = maxWidth / pageWidth;
        pageWidth = maxWidth;
        pageHeight = pageHeight * ratio;
      }

      if (pageHeight > maxHeight) {
        const ratio = maxHeight / pageHeight;
        pageHeight = maxHeight;
        pageWidth = pageWidth * ratio;
      }

      // Add page and image
      doc.addPage({ width: pageWidth, height: pageHeight });
      doc.image(inputPath, 0, 0, { width: pageWidth, height: pageHeight });

      doc.end();

      return new Promise((resolve, reject) => {
        stream.on('finish', () => resolve({ success: true }));
        stream.on('error', reject);
      });
    } catch (error) {
      throw new Error(`Image conversion failed: ${error.message}`);
    }
  }

  async convertTextFile(inputPath, outputPath) {
    try {
      const content = fs.readFileSync(inputPath, 'utf8');
      const doc = new PDFDocument();
      const stream = fs.createWriteStream(outputPath);
      doc.pipe(stream);

      // Add content with proper formatting
      doc.fontSize(12)
         .font('Helvetica')
         .text(content, 50, 50, {
           width: 500,
           align: 'left'
         });

      doc.end();

      return new Promise((resolve, reject) => {
        stream.on('finish', () => resolve({ success: true }));
        stream.on('error', reject);
      });
    } catch (error) {
      throw new Error(`Text file conversion failed: ${error.message}`);
    }
  }

  async convertHtmlFile(inputPath, outputPath) {
    try {
      const browser = await this.initBrowser();
      const page = await browser.newPage();

      // Read HTML content
      const htmlContent = fs.readFileSync(inputPath, 'utf8');
      
      // Set content and generate PDF
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
      
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px'
        }
      });

      fs.writeFileSync(outputPath, pdfBuffer);
      await page.close();

      return { success: true };
    } catch (error) {
      throw new Error(`HTML conversion failed: ${error.message}`);
    }
  }

  async convertCsvFile(inputPath, outputPath) {
    try {
      const csvContent = fs.readFileSync(inputPath, 'utf8');
      const rows = csvContent.split('\n').map(row => row.split(','));

      const doc = new PDFDocument({ layout: 'landscape' });
      const stream = fs.createWriteStream(outputPath);
      doc.pipe(stream);

      let y = 50;
      const cellWidth = 100;
      const cellHeight = 20;

      // Add table headers and data
      rows.forEach((row, rowIndex) => {
        if (y > 500) { // New page if needed
          doc.addPage({ layout: 'landscape' });
          y = 50;
        }

        row.forEach((cell, cellIndex) => {
          const x = 50 + (cellIndex * cellWidth);
          
          // Draw cell border
          doc.rect(x, y, cellWidth, cellHeight).stroke();
          
          // Add cell content
          doc.fontSize(8)
             .text(cell.trim(), x + 2, y + 2, {
               width: cellWidth - 4,
               height: cellHeight - 4,
               ellipsis: true
             });
        });

        y += cellHeight;
      });

      doc.end();

      return new Promise((resolve, reject) => {
        stream.on('finish', () => resolve({ success: true }));
        stream.on('error', reject);
      });
    } catch (error) {
      throw new Error(`CSV conversion failed: ${error.message}`);
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down PDF converter...');
  if (global.pdfConverter) {
    await global.pdfConverter.cleanup();
  }
  process.exit(0);
});

// Create singleton instance
if (!global.pdfConverter) {
  global.pdfConverter = new PDFConverter();
}

module.exports = global.pdfConverter;
