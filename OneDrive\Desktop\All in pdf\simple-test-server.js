const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const converter = require('./utils/pdfConverter');

const app = express();
const PORT = 3002;

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: { fileSize: 100 * 1024 * 1024 } // 100MB limit
});

// Middleware
app.use(express.static('.'));
app.use(express.json());

// Simple conversion endpoint
app.post('/convert', upload.single('file'), async (req, res) => {
  try {
    console.log('📁 File upload received:', req.file?.originalname);
    
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const inputPath = req.file.path;
    const outputPath = path.join('output', `converted-${Date.now()}.pdf`);
    
    // Ensure output directory exists
    if (!fs.existsSync('output')) {
      fs.mkdirSync('output', { recursive: true });
    }

    console.log('🔄 Starting conversion...');
    console.log('Input:', inputPath);
    console.log('Output:', outputPath);

    // Convert file
    const result = await converter.convert(inputPath, outputPath, req.file.mimetype);
    
    if (fs.existsSync(outputPath)) {
      const stats = fs.statSync(outputPath);
      console.log(`✅ Conversion successful! PDF size: ${stats.size} bytes`);
      
      if (stats.size === 0) {
        console.log('❌ WARNING: PDF file is empty!');
        return res.status(500).json({ error: 'Generated PDF is empty' });
      }
      
      res.json({
        success: true,
        message: 'Conversion successful',
        originalName: req.file.originalname,
        pdfSize: stats.size,
        downloadUrl: `/download/${path.basename(outputPath)}`
      });
    } else {
      console.log('❌ Conversion failed - output file not created');
      res.status(500).json({ error: 'Conversion failed - no output file' });
    }

    // Cleanup input file
    fs.unlinkSync(inputPath);

  } catch (error) {
    console.error('❌ Conversion error:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// Download endpoint
app.get('/download/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join('output', filename);
    
    console.log(`📥 Download request for: ${filename}`);
    
    if (!fs.existsSync(filePath)) {
      console.log('❌ File not found:', filePath);
      return res.status(404).json({ error: 'File not found' });
    }

    const stats = fs.statSync(filePath);
    console.log(`📄 Serving file: ${filePath}, Size: ${stats.size} bytes`);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', stats.size);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    fileStream.on('end', () => {
      console.log('✅ File download completed');
    });

    fileStream.on('error', (error) => {
      console.error('❌ File stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Download failed' });
      }
    });

  } catch (error) {
    console.error('❌ Download error:', error);
    if (!res.headersSent) {
      res.status(500).json({ error: 'Download failed' });
    }
  }
});

// Simple test page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple PDF Converter Test</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
            button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>🔧 Simple PDF Converter Test</h1>
        <div class="upload-area">
            <input type="file" id="fileInput" accept=".txt,.html,.jpg,.jpeg,.png,.docx,.xlsx">
            <br><br>
            <button onclick="convertFile()">Convert to PDF</button>
        </div>
        <div id="result"></div>

        <script>
            async function convertFile() {
                const fileInput = document.getElementById('fileInput');
                const resultDiv = document.getElementById('result');
                
                if (!fileInput.files[0]) {
                    resultDiv.innerHTML = '<div class="error">Please select a file first</div>';
                    return;
                }
                
                resultDiv.innerHTML = '<div>Converting...</div>';
                
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                
                try {
                    const response = await fetch('/convert', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        resultDiv.innerHTML = \`
                            <div class="success">
                                ✅ Conversion successful!<br>
                                Original: \${result.originalName}<br>
                                PDF Size: \${result.pdfSize} bytes<br>
                                <a href="\${result.downloadUrl}" target="_blank">📥 Download PDF</a>
                            </div>
                        \`;
                    } else {
                        resultDiv.innerHTML = \`<div class="error">❌ \${result.error}</div>\`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = \`<div class="error">❌ Error: \${error.message}</div>\`;
                }
            }
        </script>
    </body>
    </html>
  `);
});

app.listen(PORT, () => {
  console.log(`🚀 Simple test server running on http://localhost:${PORT}`);
  console.log('📁 Upload directory: uploads/');
  console.log('📄 Output directory: output/');
});
